/*
 * Flyway Migration: V2__update_permissions_and_auth_client.sql
 * Purpose: Seed core permissions (including :all aggregates) and default roles (ADMIN, OPERATOR) without duplication.
 * Note: Uses SQL Server IF NOT EXISTS pattern.
 */

-- Permissions (Identity)
-- sys_identity:all
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'ddef4592-cdce-4158-800d-00fb06c24109') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('ddef4592-cdce-4158-800d-00fb06c24109','sys_identity:all','Allow all identity operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Permissions (Account)
-- sys_account:all
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '5e1b9351-2f1d-4f93-871d-a3e27ce8d97d') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('5e1b9351-2f1d-4f93-871d-a3e27ce8d97d','sys_account:all','Allow all account operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Permissions (Business)
-- sys_business:all
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '83040f1d-fb72-474f-b856-3d6f5270b543') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('83040f1d-fb72-474f-b856-3d6f5270b543','sys_business:all','Allow all business operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Permissions (Claim)
-- sys_claim:all
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '04a610e6-48bc-444f-9ee7-27bef5b26fe9') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('04a610e6-48bc-444f-9ee7-27bef5b26fe9','sys_claim:all','Allow all claim operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Permissions (Consent)
-- sys_consent:all
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '7f81915c-161c-4eb8-b72f-a02839c04e69') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('7f81915c-161c-4eb8-b72f-a02839c04e69','sys_consent:all','Allow all consent operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Permissions (Role)
-- sys_role:all
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'b3077820-bc48-4a89-be3c-bf1374b7eb51') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('b3077820-bc48-4a89-be3c-bf1374b7eb51','sys_role:all','Allow all role operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Permissions (Permission)
-- sys_permission:all
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '05f9c70b-df37-4dad-8180-c03fdaa31b57') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('05f9c70b-df37-4dad-8180-c03fdaa31b57','sys_permission:all','Allow all permission operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Permissions (Group)
-- sys_group:all
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '63ff405d-100f-4955-b818-454e13d5b171') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('63ff405d-100f-4955-b818-454e13d5b171','sys_group:all','Allow all group operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Assign all permissions to ADMIN role
INSERT INTO role_permission (role_id, permission_name)
SELECT '9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a', p.id FROM permission p
WHERE p.name LIKE '%:all'
AND NOT EXISTS (
    SELECT 1 FROM role_permission rp WHERE rp.role_id = '9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a' AND rp.permission_name = p.id
);

-- Ensure all OAuth2 registered clients have role_admin scope
-- Appends ",role_admin" only if not already present (idempotent)
UPDATE oauth2_registered_client
SET scopes = CASE
    WHEN scopes IS NULL OR LTRIM(RTRIM(scopes)) = '' THEN 'role_admin'
    WHEN scopes LIKE '%role_admin%' THEN scopes
    ELSE scopes + ',role_admin'
END;
