-- H2 Database Schema Creation for Auth Server
-- This creates all necessary tables for integration tests

-- Create permission table
CREATE TABLE IF NOT EXISTS permission (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description VARCHAR(1000),
    created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create role table
CREATE TABLE IF NOT EXISTS role (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description VARCHAR(1000),
    business_id VARCHAR(36),
    created_by VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create role_permission junction table
CREATE TABLE IF NOT EXISTS role_permission (
    role_id VARCHAR(36) NOT NULL,
    permission_name VARCHAR(36) NOT NULL,
    PRIMARY KEY (role_id, permission_name),
    FOREIGN KEY (role_id) REFERENCES role(id),
    FOREIGN KEY (permission_name) REFERENCES permission(id)
);

-- Create oauth2_registered_client table
CREATE TABLE IF NOT EXISTS oauth2_registered_client (
    id VARCHAR(100) PRIMARY KEY,
    client_id VARCHAR(100) UNIQUE NOT NULL,
    client_id_issued_at TIMESTAMP NOT NULL,
    client_secret VARCHAR(200),
    client_secret_expires_at TIMESTAMP,
    client_name VARCHAR(200) NOT NULL,
    client_authentication_methods VARCHAR(1000) NOT NULL,
    authorization_grant_types VARCHAR(1000) NOT NULL,
    redirect_uris VARCHAR(1000),
    post_logout_redirect_uris VARCHAR(1000),
    scopes VARCHAR(1000) NOT NULL,
    client_settings TEXT,
    token_settings TEXT,
    created_by VARCHAR(255) NOT NULL DEFAULT 'SYSTEM',
    updated_by VARCHAR(255) NOT NULL DEFAULT 'SYSTEM',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create oauth2_authorization table
CREATE TABLE IF NOT EXISTS oauth2_authorization (
    id VARCHAR(100) PRIMARY KEY,
    registered_client_id VARCHAR(100) NOT NULL,
    principal_name VARCHAR(200) NOT NULL,
    authorization_grant_type VARCHAR(100) NOT NULL,
    authorized_scopes VARCHAR(1000),
    attributes TEXT,
    state VARCHAR(500),
    authorization_code_value TEXT,
    authorization_code_issued_at TIMESTAMP,
    authorization_code_expires_at TIMESTAMP,
    authorization_code_metadata TEXT,
    access_token_value TEXT,
    access_token_issued_at TIMESTAMP,
    access_token_expires_at TIMESTAMP,
    access_token_metadata TEXT,
    access_token_type VARCHAR(100),
    access_token_scopes VARCHAR(1000),
    oidc_id_token_value TEXT,
    oidc_id_token_issued_at TIMESTAMP,
    oidc_id_token_expires_at TIMESTAMP,
    oidc_id_token_metadata TEXT,
    refresh_token_value TEXT,
    refresh_token_issued_at TIMESTAMP,
    refresh_token_expires_at TIMESTAMP,
    refresh_token_metadata TEXT,
    user_code_value TEXT,
    user_code_issued_at TIMESTAMP,
    user_code_expires_at TIMESTAMP,
    user_code_metadata TEXT,
    device_code_value TEXT,
    device_code_issued_at TIMESTAMP,
    device_code_expires_at TIMESTAMP,
    device_code_metadata TEXT
);

-- Create oauth2_authorization_consent table
CREATE TABLE IF NOT EXISTS oauth2_authorization_consent (
    registered_client_id VARCHAR(100) NOT NULL,
    principal_name VARCHAR(200) NOT NULL,
    authorities VARCHAR(1000) NOT NULL,
    PRIMARY KEY (registered_client_id, principal_name)
);

-- Create identities table
CREATE TABLE IF NOT EXISTS identities (
    id VARCHAR(36) PRIMARY KEY,
    identity_type VARCHAR(50) NOT NULL DEFAULT 'NONE',
    name VARCHAR(255),
    metadata TEXT,
    lifecycle_state VARCHAR(50) NOT NULL DEFAULT 'NONE',
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create identity_provider table
CREATE TABLE IF NOT EXISTS identity_provider (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description VARCHAR(1024),
    metadata TEXT,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create account table
CREATE TABLE IF NOT EXISTS account (
    id VARCHAR(36) PRIMARY KEY,
    account_type VARCHAR(50) NOT NULL,
    business_id VARCHAR(36),
    identity_id VARCHAR(36),
    identity_provider_id VARCHAR(36),
    metadata TEXT,
    lifecycle_state VARCHAR(50) NOT NULL DEFAULT 'NONE',
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(account_type, business_id, identity_provider_id, identity_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_identity_name ON identities(name);
CREATE INDEX IF NOT EXISTS idx_identity_type ON identities(identity_type);
CREATE INDEX IF NOT EXISTS idx_role_business ON role(business_id);
CREATE INDEX IF NOT EXISTS idx_account_business_id ON account(business_id);
CREATE INDEX IF NOT EXISTS idx_account_identity_id ON account(identity_id);
CREATE INDEX IF NOT EXISTS idx_oauth2_client_id ON oauth2_registered_client(client_id);
