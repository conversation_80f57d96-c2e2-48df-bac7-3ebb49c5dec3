-- H2 Database Seed Data for Auth Server Tests
-- This inserts basic permissions, roles, and OAuth2 clients needed for tests

-- Insert core permissions
INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES 
('ddef4592-cdce-4158-800d-00fb06c24109','sys_identity:all','Allow all identity operations','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP),
('5e1b9351-2f1d-4f93-871d-a3e27ce8d97d','sys_account:all','Allow all account operations','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP),
('83040f1d-fb72-474f-b856-3d6f5270b543','sys_business:all','Allow all business operations','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP),
('04a610e6-48bc-444f-9ee7-27bef5b26fe9','sys_claim:all','Allow all claim operations','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP),
('7f81915c-161c-4eb8-b72f-a02839c04e69','sys_consent:all','Allow all consent operations','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP),
('b3077820-bc48-4a89-be3c-bf1374b7eb51','sys_role:all','Allow all role operations','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP),
('05f9c70b-df37-4dad-8180-c03fdaa31b57','sys_permission:all','Allow all permission operations','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP),
('63ff405d-100f-4955-b818-454e13d5b171','sys_group:all','Allow all group operations','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP);

-- Insert default roles
INSERT INTO role (id, name, description, created_by, updated_by, created_at, updated_at) VALUES 
('9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a','ADMIN','Administrator role with full permissions','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP),
('8e96a6e5-a7c3-4cb3-b7b6-0531a70f752b','OPERATOR','Operator role with limited permissions','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP);

-- Assign all permissions to ADMIN role
INSERT INTO role_permission (role_id, permission_name)
SELECT '9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a', p.id FROM permission p
WHERE p.name LIKE '%:all';

-- Insert test OAuth2 client
INSERT INTO oauth2_registered_client (
    id, 
    client_id, 
    client_id_issued_at, 
    client_secret, 
    client_name, 
    client_authentication_methods, 
    authorization_grant_types, 
    redirect_uris, 
    scopes,
    client_settings,
    token_settings,
    created_by,
    updated_by,
    created_at,
    updated_at
) VALUES (
    'test-client-id',
    'test-client',
    CURRENT_TIMESTAMP,
    '{noop}test-secret',
    'Test Client',
    'client_secret_basic,client_secret_post',
    'authorization_code,refresh_token,client_credentials',
    'http://localhost:8080/callback',
    'openid,profile,email,role_admin',
    '{"@class":"java.util.Collections$UnmodifiableMap","settings.client.require-proof-key":false,"settings.client.require-authorization-consent":false}',
    '{"@class":"java.util.Collections$UnmodifiableMap","settings.token.reuse-refresh-tokens":true,"settings.token.id-token-signature-algorithm":["org.springframework.security.oauth2.jose.jws.SignatureAlgorithm","RS256"],"settings.token.access-token-time-to-live":["java.time.Duration",300.000000000],"settings.token.access-token-format":{"@class":"org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat","value":"self-contained"},"settings.token.refresh-token-time-to-live":["java.time.Duration",3600.000000000],"settings.token.authorization-code-time-to-live":["java.time.Duration",300.000000000],"settings.token.device-code-time-to-live":["java.time.Duration",300.000000000]}',
    'SYSTEM',
    'SYSTEM',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert test identity provider
INSERT INTO identity_provider (id, name, description, metadata, created_by, updated_by, created_at, updated_at) VALUES 
('test-provider-id','test-provider','Test Identity Provider','{}','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP);

-- Insert test identity
INSERT INTO identities (id, identity_type, name, metadata, lifecycle_state, created_by, updated_by, created_at, updated_at) VALUES 
('test-identity-id','USER','test-user','{}','ACTIVE','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP);

-- Insert test account
INSERT INTO account (id, account_type, business_id, identity_id, identity_provider_id, metadata, lifecycle_state, created_by, updated_by, created_at, updated_at) VALUES 
('test-account-id','USER',NULL,'test-identity-id','test-provider-id','{}','ACTIVE','SYSTEM','SYSTEM',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP);
