/*
 * Flyway Migration: V1__baseline.sql
 * Purpose: Create the database schema.
 * Note: Database initial script.
 */

CREATE TABLE account (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), business_id UNIQUEidentifier not null, id UNIQUEidentifier not null, identity_id UNIQUEidentifier not null, identity_provider_id UNIQUEidentifier not null, merge_primary UNIQUEidentifier, merge_secondary UNIQUEidentifier, account_type varchar(255) not null check (account_type in ('NONE','LOCAL','FEDERATED','SOCIAL','CUSTOMER','WORKFORCE','SERVICE')), created_by varchar(255) not null, lifecycle_state varchar(255) not null check (lifecycle_state in ('NONE','PROVISIONED','PENDING','ACTIVE','SUSPENDED','DEACTIVATED','DELETED')), metadata varchar(255), status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_account_id primary key (id))
CREATE TABLE account_group (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), id UNIQUEidentifier not null, description varchar(1024), created_by varchar(255) not null, metadata varchar(255), name varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_account_group_id primary key (id))
CREATE TABLE account_group_role (account_group_id UNIQUEidentifier not null, role_id UNIQUEidentifier not null, primary key (account_group_id, role_id))
CREATE TABLE account_role (account_id UNIQUEidentifier not null, role_id UNIQUEidentifier not null, primary key (account_id, role_id))
CREATE TABLE business (accent_color varchar(7), created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), id UNIQUEidentifier not null, business_type varchar(255) not null check (business_type in ('TENANT','LEGAL_ENTITY','BUSINESS_UNIT')), created_by varchar(255) not null, description varchar(255), metadata varchar(max), name varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), logo varchar(max), primary key (id))
CREATE TABLE channel (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), id UNIQUEidentifier not null, channel_type varchar(255) not null check (channel_type in ('WEB','MOBILE_APP','ATM','POS','API','ON_SITE','AUTOMATION','DEFAULT')), created_by varchar(255) not null, description varchar(255), metadata varchar(255), name varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_channel_id primary key (id))
CREATE TABLE claim_definition (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), id UNIQUEidentifier not null, is_a_list_of UNIQUEidentifier, data_format varchar(1024), description varchar(1024), claim_type varchar(255) default 'USER_DEFINED' not null check (claim_type in ('USER_DEFINED','SYSTEM_DEFINED')), code varchar(255) not null, created_by varchar(255) not null, data_type varchar(255) check (data_type in ('STRING','NUMERIC','BOOL','IMAGE','ARRAY','DATE','DATETIME')), name varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_claim_def_id primary key (id))
CREATE TABLE claim_set (is_identifier bit not null, created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), business_id UNIQUEidentifier, id UNIQUEidentifier not null, description varchar(1024), account_type varchar(255) check (account_type in ('NONE','LOCAL','FEDERATED','SOCIAL','CUSTOMER','WORKFORCE','SERVICE')), created_by varchar(255) not null, lookup_strategy varchar(255) not null check (lookup_strategy in ('ALL_CLAIMS_MUST_MATCH','ANY_CLAIM_CAN_MATCH')), name varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_claim_set_id primary key (id))
CREATE TABLE claim_set_claim_value (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), claim_set_id UNIQUEidentifier not null, claim_value_id UNIQUEidentifier not null, id UNIQUEidentifier not null, created_by varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), primary key (id))
CREATE TABLE claim_set_definition_mapping (claim_definition_order int, enforce_UNIQUEness bit not null, created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), claim_definition_id UNIQUEidentifier not null, claim_set_id UNIQUEidentifier not null, id UNIQUEidentifier not null, created_by varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_claim_set_def_mapping_id primary key (id))
CREATE TABLE claim_value (is_computed bit not null, is_primary bit not null, primary_index int, created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), claim_definition_id UNIQUEidentifier, claim_verification_id UNIQUEidentifier, id UNIQUEidentifier not null, owner_id UNIQUEidentifier not null, value varchar(1024), created_by varchar(255) not null, owner_type varchar(255) not null check (owner_type in ('IDENTITY','ACCOUNT')), source varchar(255), status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), primary key (id))
CREATE TABLE claim_verification (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), verified_at datetimeoffset(6), claim_value_id UNIQUEidentifier, id UNIQUEidentifier not null, created_by varchar(255) not null, result varchar(255) not null check (result in ('NONE','PENDING','OK','FAIL')), status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), verification_type varchar(255) not null, constraint uk_claim_verif_id primary key (id))
CREATE TABLE group_membership (is_active bit not null, created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), account_id UNIQUEidentifier not null, group_id UNIQUEidentifier not null, id UNIQUEidentifier not null, created_by varchar(255) not null, membership_role varchar(255) check (membership_role in ('OWNER','MEMBER')), status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_group_membership_id primary key (id))
CREATE TABLE identities (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), id UNIQUEidentifier not null, merged_to UNIQUEidentifier, created_by varchar(255) not null, identity_type varchar(255) not null check (identity_type in ('NONE','PERSON','FEDERATED','DEVICE','SERVICE','ORGANIZATION')), lifecycle_state varchar(255) not null check (lifecycle_state in ('NONE','REGISTERED','PENDING','ACTIVE','SUSPENDED','BLOCKED','DEACTIVATED','INACTIVE','DELETED','TERMINATED')), metadata varchar(255), name varchar(255), status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_identity_id primary key (id))
CREATE TABLE identity_provider (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), id UNIQUEidentifier not null, description varchar(1024), created_by varchar(255) not null, metadata varchar(255), name varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_identity_provider_id primary key (id))
CREATE TABLE oauth2_authorization (access_token_type varchar(100), authorization_grant_type varchar(100) not null, id varchar(100) not null, registered_client_id varchar(100) not null, principal_name varchar(200) not null, state varchar(500), access_token_scopes varchar(1000), authorized_scopes varchar(1000), access_token_expires_at varchar(255), access_token_issued_at varchar(255), authorization_code_expires_at varchar(255), authorization_code_issued_at varchar(255), device_code_expires_at varchar(255), device_code_issued_at varchar(255), oidc_id_token_expires_at varchar(255), oidc_id_token_issued_at varchar(255), refresh_token_expires_at varchar(255), refresh_token_issued_at varchar(255), user_code_expires_at varchar(255), user_code_issued_at varchar(255), access_token_metadata varchar(max), access_token_value varchar(max), attributes varchar(max), authorization_code_metadata varchar(max), authorization_code_value varchar(max), device_code_metadata varchar(max), device_code_value varchar(max), oidc_id_token_metadata varchar(max), oidc_id_token_value varchar(max), refresh_token_metadata varchar(max), refresh_token_value varchar(max), user_code_metadata varchar(max), user_code_value varchar(max), primary key (id))
CREATE TABLE oauth2_authorization_consent (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), registered_client_id varchar(100) not null, principal_name varchar(200) not null, authorities varchar(1000) not null, created_by varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), primary key (registered_client_id, principal_name))
CREATE TABLE oauth2_authorized_client (access_token_type varchar(100) not null, client_registration_id varchar(100) not null, principal_name varchar(200) not null, access_token_scopes varchar(1000), access_token_expires_at varchar(255) not null, access_token_issued_at varchar(255) not null, created_at varchar(255) not null, refresh_token_issued_at varchar(255), access_token_value varchar(max) not null, refresh_token_value varchar(max), primary key (client_registration_id, principal_name))
CREATE TABLE oauth2_registered_client (access_token_time_to_live int, authorization_code_time_to_live int, device_code_time_to_live int, refresh_token_time_to_live int, require_authorization_consent bit, require_proof_key bit, reuse_refresh_tokens bit, x509certificate_bound_access_tokens bit, client_id_issued_at datetimeoffset(6) not null, client_secret_expires_at datetimeoffset(6), created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), client_id varchar(100) not null, id varchar(100) not null, client_name varchar(200) not null, client_secret varchar(200), authorization_grant_types varchar(1000) not null, client_authentication_methods varchar(1000) not null, post_logout_redirect_uris varchar(1000), redirect_uris varchar(1000), scopes varchar(1000) not null, access_token_format varchar(255), created_by varchar(255) not null, id_token_signature_algorithm varchar(255), jwk_set_url varchar(255), status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), token_endpoint_authentication_signing_algorithm varchar(255), updated_by varchar(255), x509certificate_subjectdn varchar(255), primary key (id))
CREATE TABLE permission (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), id UNIQUEidentifier not null, created_by varchar(255) not null, description varchar(255), name varchar(255), status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), primary key (id))
CREATE TABLE role (created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), business_id UNIQUEidentifier, id UNIQUEidentifier not null, description varchar(1000), created_by varchar(255) not null, name varchar(255) not null, status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_role_id primary key (id))
CREATE TABLE role_permission (permission_name UNIQUEidentifier not null, role_id UNIQUEidentifier not null, primary key (permission_name, role_id))
CREATE TABLE scim_to_claim_mapping (sequence_order int not null, created_at datetimeoffset(6) not null, updated_at datetimeoffset(6), business_id UNIQUEidentifier not null, claim_definition_REFERENCESd UNIQUEidentifier, claim_set_origin UNIQUEidentifier, claim_set_REFERENCESd UNIQUEidentifier, id UNIQUEidentifier not null, created_by varchar(255) not null, filter_mapping varchar(255), status varchar(255) default 'ACTIVE' not null check (status in ('ACTIVE','INACTIVE','DELETED')), updated_by varchar(255), constraint uk_scim_to_claim_mapping_id primary key (id))
CREATE TABLE version_server (applied_at datetimeoffset(6), updated_at datetimeoffset(6), build_version varchar(255), name varchar(255) not null, version varchar(255) not null, primary key (name, version))
CREATE INDEX idx_account_business_id ON account (business_id)
CREATE INDEX idx_account_identity_id ON account (identity_id)
ALTER TABLE account ADD CONSTRAINT uk_account_type_business_identity_provider_identity UNIQUE (account_type, business_id, identity_provider_id, identity_id)
ALTER TABLE account_group ADD CONSTRAINT uk_account_group_name UNIQUE (name)
ALTER TABLE business ADD CONSTRAINT UK5iyafjam5laj1n93tm5e4q7hm UNIQUE (name)
CREATE INDEX idx_channel_business_id ON channel (business_id)
ALTER TABLE channel ADD CONSTRAINT UK1r44jjdpx9o6wabic55qp3mgm UNIQUE (name)
ALTER TABLE claim_definition ADD CONSTRAINT uk_claim_def_code UNIQUE (code)
CREATE INDEX idx_claim_set_composite ON claim_set (business_id, account_type, is_identifier)
CREATE INDEX idx_claim_set_business_account ON claim_set (business_id, account_type)
CREATE UNIQUE nonclustered index uk_claim_set_business_account_name ON claim_set (business_id, account_type, name) WHERE business_id is not null and account_type is not null and name is not null
CREATE INDEX idx_claim_set ON claim_set_claim_value (claim_set_id)
CREATE INDEX idx_claim_value ON claim_set_claim_value (claim_value_id)
ALTER TABLE claim_set_claim_value ADD CONSTRAINT UKh0j87kkkhtu815tkdce3tmv6b UNIQUE (claim_value_id)
ALTER TABLE claim_set_definition_mapping ADD CONSTRAINT uk_claim_set_def_mapping_composite UNIQUE (claim_set_id, claim_definition_id)
CREATE INDEX idx_claim_value_definition ON claim_value (claim_definition_id)
CREATE INDEX idx_claim_value_owner ON claim_value (owner_type, owner_id)
CREATE UNIQUE nonclustered index UK7k7gl6frxd9gtuekgsf6c4jel ON claim_value (claim_verification_id) WHERE claim_verification_id is not null
CREATE INDEX idx_claim_verif_value ON claim_verification (claim_value_id)
CREATE INDEX idx_claim_verif_type ON claim_verification (verification_type)
CREATE INDEX idx_claim_verif_result ON claim_verification (result)
CREATE INDEX idx_group_membership_acc_grp ON group_membership (account_id, group_id)
CREATE INDEX idx_group_membership_acc ON group_membership (account_id)
CREATE INDEX idx_group_membership_grp ON group_membership (group_id)
CREATE INDEX idx_identity_name ON identities (name)
CREATE INDEX idx_identity_type ON identities (identity_type)
ALTER TABLE identity_provider ADD CONSTRAINT uk_identity_provider_name UNIQUE (name)
ALTER TABLE oauth2_registered_client ADD CONSTRAINT UKsdq6sc2ye4wfgtsgutyn67es5 UNIQUE (client_id)
CREATE UNIQUE nonclustered index UK2ojme20jpga3r4r79tdso17gi ON permission (name) WHERE name is not null
CREATE INDEX idx_role_business ON role (business_id)
ALTER TABLE role ADD CONSTRAINT uk_role_name UNIQUE (name)
CREATE INDEX uk_scim_to_claim_mapping_business_id ON scim_to_claim_mapping (business_id)
ALTER TABLE account ADD CONSTRAINT fk_account_business FOREIGN KEY (business_id) REFERENCES business
ALTER TABLE account ADD CONSTRAINT FK8lbmdi5hpc5okaeqm6cf97au4 FOREIGN KEY (identity_id) REFERENCES identities
ALTER TABLE account ADD CONSTRAINT fk_account_identity_provider FOREIGN KEY (identity_provider_id) REFERENCES identity_provider
ALTER TABLE account_group_role ADD CONSTRAINT fk_account_group_role_role FOREIGN KEY (role_id) REFERENCES role
ALTER TABLE account_group_role ADD CONSTRAINT fk_account_group_role_group FOREIGN KEY (account_group_id) REFERENCES account_group
ALTER TABLE account_role ADD CONSTRAINT fk_account_role_account FOREIGN KEY (account_id) REFERENCES account
ALTER TABLE account_role ADD CONSTRAINT fk_account_role_role FOREIGN KEY (role_id) REFERENCES role
ALTER TABLE claim_definition ADD CONSTRAINT FK21ncn2cgn2k5cglv5yi1wt0oa FOREIGN KEY (is_a_list_of) REFERENCES claim_definition
ALTER TABLE claim_set ADD CONSTRAINT fk_claim_set_business FOREIGN KEY (business_id) REFERENCES business
ALTER TABLE claim_set_claim_value ADD CONSTRAINT fk_claim_set_claim_value_claim_set FOREIGN KEY (claim_set_id) REFERENCES claim_set
ALTER TABLE claim_set_claim_value ADD CONSTRAINT fk_claim_value_claim_set_claim_value FOREIGN KEY (claim_value_id) REFERENCES claim_value
ALTER TABLE claim_set_definition_mapping ADD CONSTRAINT fk_claim_set_def_mapping_claim_def FOREIGN KEY (claim_definition_id) REFERENCES claim_definition
ALTER TABLE claim_set_definition_mapping ADD CONSTRAINT fk_claim_set_def_mapping_claim_set FOREIGN KEY (claim_set_id) REFERENCES claim_set
ALTER TABLE claim_value ADD CONSTRAINT fk_claim_value_definition FOREIGN KEY (claim_definition_id) REFERENCES claim_definition
ALTER TABLE claim_value ADD CONSTRAINT FKh8keiwr0ro9djgg7y4jaujils FOREIGN KEY (claim_verification_id) REFERENCES claim_verification
ALTER TABLE claim_verification ADD CONSTRAINT FK7rx9u0nn8wwubh3sceyoyvnwg FOREIGN KEY (claim_value_id) REFERENCES claim_value
ALTER TABLE group_membership ADD CONSTRAINT fk_group_membership_account FOREIGN KEY (account_id) REFERENCES account
ALTER TABLE group_membership ADD CONSTRAINT fk_group_membership_group FOREIGN KEY (group_id) REFERENCES account_group
ALTER TABLE role_permission ADD CONSTRAINT FKldkc3yoh80x16gv94519otli4 FOREIGN KEY (permission_name) REFERENCES permission
ALTER TABLE role_permission ADD CONSTRAINT FKa6jx8n8xkesmjmv6jqug6bg68 FOREIGN KEY (role_id) REFERENCES role
ALTER TABLE scim_to_claim_mapping ADD CONSTRAINT fk_account_business FOREIGN KEY (business_id) REFERENCES business