package com.vusecurity.auth.shared.config;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.DisabledIf;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.PlatformTransactionManager;
import org.testcontainers.DockerClientFactory;

import javax.sql.DataSource;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test to verify that the TestMultitenantConfiguration properly disables
 * multitenant mode and provides single datasource configuration.
 * NOTE: This test is disabled when Docker is not available since it requires
 * TestContainers to run.
 */
@SpringBootTest
@ActiveProfiles("test")
@Import(TestMultitenantService.class)
@DisabledIf("isDockerNotAvailable")
class TestMultitenantConfigurationTest {

    /**
     * Check if Docker is not available for TestContainers.
     * This method is used by @DisabledIf to conditionally disable the test.
     */
    static boolean isDockerNotAvailable() {
        try {
            DockerClientFactory.instance().client();
            return false; // Docker is available
        } catch (Exception e) {
            System.out.println("Docker not available, disabling TestMultitenantConfigurationTest: " + e.getMessage());
            return true; // Docker is not available
        }
    }

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private LocalContainerEntityManagerFactoryBean entityManagerFactory;

    @Test
    void shouldConfigureSingleDatasourceMode() {
        // Verify that we have a single datasource configured
        assertThat(dataSource).isNotNull();
        assertThat(jdbcTemplate).isNotNull();
        assertThat(transactionManager).isNotNull();
        assertThat(entityManagerFactory).isNotNull();
    }

    @Test
    void shouldNotHaveMultitenantProperties() {
        // Verify that multitenant properties are not set in the EntityManagerFactory
        Map<String, Object> jpaProperties = entityManagerFactory.getJpaPropertyMap();

        // These properties should not be present in single-tenant mode
        assertThat(jpaProperties).doesNotContainKey("hibernate.multiTenancy");
        assertThat(jpaProperties).doesNotContainKey("hibernate.tenant_identifier_resolver");
        assertThat(jpaProperties).doesNotContainKey("hibernate.multi_tenant_connection_provider");
    }

    @Test
    void shouldBeAbleToExecuteSimpleQuery() {
        // Verify that we can execute a simple query against the test database
        Integer result = jdbcTemplate.queryForObject("SELECT 1", Integer.class);
        assertThat(result).isEqualTo(1);
    }
}
